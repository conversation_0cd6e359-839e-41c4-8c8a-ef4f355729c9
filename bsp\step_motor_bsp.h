/**
 * @file step_motor_bsp.h
 * @brief 步进电机BSP模块 - 基于Emm_V5驱动的XY轴双轴步进电机控制系统
 * @version 1.0
 * @date 2025-01-29
 *
 * ============================================================================
 *                           步进电机BSP模块使用指南
 * ============================================================================
 *
 * 【项目概述】
 * 本模块为STM32F407运动目标控制与自动追踪系统的步进电机控制层，基于Emm_V5
 * 步进电机驱动器实现XY轴双轴精确控制，支持速度控制和位置控制两种模式。
 *
 * 【硬件配置】
 * - MCU: STM32F407
 * - 驱动器: Emm_V5步进电机驱动器
 * - 通信: UART串口通信
 * - X轴电机: 通过UART2(huart2)控制，地址0x01
 * - Y轴电机: 通过UART4(huart4)控制，地址0x01
 *
 * 【核心参数配置】
 * MOTOR_MAX_SPEED: 3 RPM     - 电机最大转速限制
 * MOTOR_ACCEL: 0             - 加速度(0=直接启动)
 * MOTOR_MAX_ANGLE: ±50°      - 角度限制范围
 * MOTOR_SYNC_FLAG: false     - 非同步控制模式
 *
 * 【使用流程】
 * 1. 系统初始化时调用 Step_Motor_Init()
 * 2. 根据需要选择控制模式:
 *    - 百分比速度控制: Step_Motor_Set_Speed()
 *    - 精确RPM控制: Step_Motor_Set_Speed_my()
 *    - 位置控制: Step_Motor_Set_Pwm()
 * 3. 需要停止时调用 Step_Motor_Stop()
 *
 * 【速度控制示例】
 * // 百分比控制: X轴50%正转，Y轴30%反转
 * Step_Motor_Set_Speed(50, -30);
 *
 * // RPM精确控制: X轴1.5RPM正转，Y轴2.0RPM反转
 * Step_Motor_Set_Speed_my(1.5f, -2.0f);
 *
 * 【位置控制示例】
 * // X轴正转1000脉冲，Y轴反转500脉冲
 * Step_Motor_Set_Pwm(1000, -500);
 *
 * 【注意事项】
 * - 确保串口huart2和huart4已正确初始化
 * - 速度值自动限制在MOTOR_MAX_SPEED范围内
 * - 正值=CW(顺时针)，负值=CCW(逆时针)
 * - 调试信息通过huart1输出
 * - RPM控制精度为0.1RPM，<0.05RPM将量化为0
 *
 * ============================================================================
 */

#ifndef __STEP_MOTOR_BSP_H__
#define __STEP_MOTOR_BSP_H__

#include "bsp_system.h"

/* 电机控制宏定义 */
#define MOTOR_X_ADDR        0x01          // X轴电机地址
#define MOTOR_Y_ADDR        0x01          // Y轴电机地址
#define MOTOR_X_UART        huart2        // X轴电机串口(下方)
#define MOTOR_Y_UART        huart4        // Y轴电机串口(上方)
#define MOTOR_MAX_SPEED     3             // 电机最大转速(RPM)
#define MOTOR_ACCEL         0             // 电机加速度(0表示直接启动)
#define MOTOR_SYNC_FLAG     false         // 电机同步标志
#define MOTOR_MAX_ANGLE     50            // 电机最大角度限制(±50°)

/* 函数声明 */
/**
 * @brief 步进电机系统初始化
 * @note 系统启动时必须首先调用，使能XY轴电机并初始停止
 */
void Step_Motor_Init(void);

/**
 * @brief 百分比速度控制
 * @param x_percent X轴速度百分比(-100~100)，正值CW，负值CCW
 * @param y_percent Y轴速度百分比(-100~100)，正值CW，负值CCW
 * @note 实际RPM = (百分比 × MOTOR_MAX_SPEED) / 100
 */
void Step_Motor_Set_Speed(int8_t x_percent, int8_t y_percent);

/**
 * @brief 精确RPM速度控制
 * @param x_rpm X轴目标速度(RPM)，支持负值反向，精度0.1RPM
 * @param y_rpm Y轴目标速度(RPM)，支持负值反向，精度0.1RPM
 * @note 自动限制在[-MOTOR_MAX_SPEED, MOTOR_MAX_SPEED]范围内
 */
void Step_Motor_Set_Speed_my(float x_rpm, float y_rpm);

/**
 * @brief 位置控制(脉冲模式)
 * @param x_distance X轴移动脉冲数，正值CW，负值CCW
 * @param y_distance Y轴移动脉冲数，正值CW，负值CCW
 * @note 相对运动模式，使用MOTOR_MAX_SPEED速度执行
 */
void Step_Motor_Set_Pwm(int32_t x_distance, int32_t y_distance);

/**
 * @brief 立即停止所有电机
 * @note 紧急停止、模式切换、系统复位时使用
 */
void Step_Motor_Stop(void);

/**
 * @brief 电机处理函数
 * @note 可用于周期性状态检查或控制逻辑(当前空实现)
 */
void step_motor_proc(void);

#endif

