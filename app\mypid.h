/**
 * @file mypid.h
 * @brief 多功能PID控制器模块 - 支持位置式/增量式PID及多种高级算法
 * @version 2.0
 * @date 2025-01-29
 *
 * ============================================================================
 *                           PID控制器模块使用指南
 * ============================================================================
 *
 * 【模块概述】
 * 本模块为运动控制系统提供完整的PID控制解决方案，支持多种PID算法：
 * - 标准位置式PID (POSITION_PID)
 * - 增量式PID (DELTA_PID)
 * - 积分分离PID (防积分饱和)
 * - 微分先行PID (减少微分冲击)
 * - 角度PID (处理0-360°循环)
 * - 偏航角PID (处理-180°~180°跳变)
 *
 * 【核心特性】
 * ✓ 输入/输出死区处理        ✓ 积分限幅防饱和
 * ✓ 误差平滑滤波           ✓ 多种PID算法
 * ✓ 函数指针灵活配置        ✓ 角度处理专用算法
 * ✓ 实时参数调整           ✓ 完整的状态监控
 *
 * 【应用场景】
 * - 步进电机位置控制 (pid_x, pid_y)
 * - 直流电机速度控制 (pid_speed_left/right)
 * - 位置外环控制 (pid_location_left/right)
 * - 云台角度控制 (角度PID)
 * - 机器人导航控制 (偏航角PID)
 *
 * 【快速开始】
 * 1. 调用 PID_INIT() 初始化所有PID实例
 * 2. 根据控制对象选择合适的PID算法:
 *    - 位置控制: pid_calc()
 *    - 防积分饱和: pid_calc_i_separation()
 *    - 角度控制: pid_angle_calc() 或 pid_yaw_calc()
 * 3. 在控制循环中调用相应的计算函数
 *
 * 【PID参数调试指南】
 * Kp (比例): 响应速度，过大会振荡
 * Ki (积分): 消除静差，过大会超调
 * Kd (微分): 减少超调，过大会放大噪声
 *
 * 调试顺序: Kp → Kd → Ki
 *
 * 【使用示例】
 * // 初始化
 * PID_INIT();
 *
 * // 位置控制
 * float output = pid_calc(&pid_x, current_pos, target_pos, 1);
 *
 * // 积分分离控制(防饱和)
 * float output = pid_calc_i_separation(&pid_x, current, target, 1, 10.0f);
 *
 * // 角度控制
 * float output = pid_yaw_calc(&pid_angle, current_yaw, target_yaw, 1);
 *
 * ============================================================================
 */

#ifndef  __MYPID_H__
#define  __MYPID_H__

/* Standard Includes */
#include "bsp_system.h"

/* 历史类型定义(已弃用，保留兼容性) */
//typedef unsigned char uint8;       // 无符号  8 bits
//typedef unsigned short int uint16; // 无符号 16 bits
//// typedef unsigned long int uint32;  // 无符号 32 bits
//// typedef unsigned long long uint64; // 无符号 64 bits

//typedef char int8;       // 有符号  8 bits
//typedef short int int16; // 有符号 16 bits
//// typedef long int int32;  // 有符号 32 bits
//// typedef long long int64; // 有符号 64 bits

//typedef volatile uint8 vuint8;   // 易变性修饰 无符号  8 bits
//typedef volatile uint16 vuint16; // 易变性修饰 无符号 16 bits
//// typedef volatile uint32 vuint32; // 易变性修饰 无符号 32 bits
//// typedef volatile uint64 vuint64; // 易变性修饰 无符号 64 bits

//typedef volatile int8 vint8;   // 易变性修饰 有符号  8 bits
//typedef volatile int16 vint16; // 易变性修饰 有符号 16 bits
//// typedef volatile int32 vint32; // 易变性修饰 有符号 32 bits
//// typedef volatile int64 vint64; // 易变性修饰 有符号 64 bits
//// #define uint32_t uint16
/**
 * @brief PID误差历史索引和控制模式枚举
 *
 * 【误差历史索引】
 * LLAST: 前前次误差 err[0] - 用于增量式PID的二阶微分计算
 * LAST:  前次误差 err[1]   - 用于微分项计算
 * NOW:   当前误差 err[2]   - 当前控制周期的误差值
 *
 * 【PID控制模式】
 * POSITION_PID: 位置式PID - 输出为绝对控制量，适用于位置控制
 * DELTA_PID:    增量式PID - 输出为控制量增量，适用于速度控制
 */
enum
{
  LLAST = 0,        // 前前次误差索引
  LAST,             // 前次误差索引
  NOW,              // 当前误差索引
  POSITION_PID,     // 位置式PID模式
  DELTA_PID,        // 增量式PID模式
};

/**
 * @brief PID控制器结构体 - 完整的PID控制器实现
 *
 * 【核心PID参数】
 * p, i, d: PID三个控制参数(Kp, Ki, Kd)
 *
 * 【输入输出变量】
 * set: 目标值(设定值)
 * get: 反馈值(测量值)
 * err[3]: 误差历史数组 [前前次, 前次, 当前]
 *
 * 【PID输出分量】
 * pout: 比例项输出 = Kp × err[NOW]
 * iout: 积分项输出 = Ki × Σerr (累积)
 * dout: 微分项输出 = Kd × (err[NOW] - err[LAST])
 * out:  最终输出 = pout + iout + dout
 *
 * 【限制和死区参数】
 * input_max_err:    输入误差限制，超过此值输出为0
 * output_deadband:  输出死区，输出小于此值时返回0
 * input_deadband:   输入死区，误差小于此值时视为0
 *
 * 【控制参数】
 * pid_mode:         PID模式(POSITION_PID/DELTA_PID)
 * max_out:          输出限幅值
 * integral_limit:   积分限幅值(防积分饱和)
 *
 * 【函数指针】
 * f_param_init:     参数初始化函数指针
 * f_pid_reset:      PID复位函数指针
 */
typedef struct pid_t
{
  /* PID控制参数 */
  float p;                      // 比例系数Kp
  float i;                      // 积分系数Ki
  float d;                      // 微分系数Kd

  /* 输入输出变量 */
  float set;                    // 目标值(设定值)
  float get;                    // 反馈值(实际测量值)
  float err[3];                 // 误差历史: [LLAST, LAST, NOW]

  /* PID输出分量 */
  float pout;                   // 比例项输出
  float iout;                   // 积分项输出
  float dout;                   // 微分项输出
  float out;                    // 最终控制输出

  /* 限制参数 */
  float input_max_err;          // 输入误差上限，超过则输出0
  float output_deadband;        // 输出死区，小于此值输出0
  float input_deadband;         // 输入死区，误差小于此值视为0

  /* 控制配置 */
  uint32_t pid_mode;            // PID模式选择
  uint32_t max_out;             // 输出限幅值
  uint32_t integral_limit;      // 积分限幅值

  /* 函数指针 - 支持运行时参数调整 */
  void (*f_param_init)(struct pid_t *pid,
                       uint32_t pid_mode,
                       uint32_t max_output,
                       uint32_t inte_limit,
                       float p,
                       float i,
                       float d);
  void (*f_pid_reset)(struct pid_t *pid, float p, float i, float d);

} pid_t;

/* ============================================================================
 *                           全局PID实例声明
 * ============================================================================ */

/**
 * @brief 预定义PID控制器实例 - 兼容历史代码
 *
 * 这些实例保留用于向后兼容，建议使用下方的新实例
 */
extern pid_t pid_motor_left;      // 左电机控制PID(历史兼容)
extern pid_t pid_motor_right;     // 右电机控制PID(历史兼容)
extern pid_t pid_left_location;   // 左侧位置控制PID(历史兼容)
extern pid_t pid_right_location;  // 右侧位置控制PID(历史兼容)


#if 0
#define PID_PARAM_DEFAULT \
  {                       \
    0,                    \
        0,                \
        0,                \
        0,                \
        0,                \
        {0, 0, 0},        \
        0,                \
        0,                \
        0,                \
        0,                \
        0,                \
        0,                \
  }\

typedef struct
{
  float p;
  float i;
  float d;

  float set;
  float get;
  float err[3]; //error

  float pout; 
  float iout; 
  float dout; 
  float out;

  float input_max_err;    //input max err;
  float output_deadband;  //output deadband; 

  float p_far;
  float p_near;
  float grade_range;
  
  uint32_t pid_mode;
  uint32_t max_out;
  uint32_t integral_limit;

  void (*f_param_init)(struct pid_t *pid, 
                       uint32_t      pid_mode,
                       uint32_t      max_output,
                       uint32_t      inte_limit,
                       float         p,
                       float         i,
                       float         d);
  void (*f_pid_reset)(struct pid_t *pid, float p, float i, float d);
 
} grade_pid_t;
#endif

/* ============================================================================
 *                           核心函数接口声明
 * ============================================================================ */

/**
 * @brief PID结构体初始化函数
 * @param pid PID控制器实例指针
 * @param mode PID模式(POSITION_PID/DELTA_PID)
 * @param maxout 输出限幅值
 * @param intergral_limit 积分限幅值
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 * @note 初始化PID参数并设置函数指针，必须在使用前调用
 */
void PID_struct_init(
    pid_t *pid,
    uint32_t mode,
    uint32_t maxout,
    uint32_t intergral_limit,
    float kp,
    float ki,
    float kd);

/**
 * @brief 系统PID初始化函数
 * @note 初始化所有预定义的PID实例，系统启动时调用
 *
 * 初始化的PID实例包括:
 * - pid_x, pid_y: 步进电机位置控制
 * - pid_speed_left/right: 直流电机速度控制
 * - pid_location_left/right: 位置外环控制
 */
void PID_INIT(void);

/**
 * @brief 标准PID计算函数
 * @param pid PID控制器实例指针
 * @param get 当前反馈值(测量值)
 * @param set 目标设定值
 * @param smoth 平滑滤波标志(0=不滤波, 1=0.7新+0.3旧)
 * @return PID控制输出值
 * @note 支持位置式和增量式PID，包含死区处理和限幅
 */
float pid_calc(pid_t *pid, float get, float set ,uint8_t smoth);

/**
 * @brief 角度PID计算函数(0-360°循环处理)
 * @param pid PID控制器实例指针
 * @param get 当前角度值(0-360°)
 * @param set 目标角度值(0-360°)
 * @param smoth 平滑滤波标志
 * @return PID控制输出值
 * @note 专用于处理0-360°角度循环，自动处理跨越边界的情况
 */
float pid_angle_calc(pid_t *pid, float get, float set ,uint8_t smoth);

/**
 * @brief 偏航角PID计算函数(-180°~180°跳变处理)
 * @param pid PID控制器实例指针
 * @param get 当前偏航角(-180°~180°)
 * @param set 目标偏航角(-180°~180°)
 * @param smoth 平滑滤波标志
 * @return PID控制输出值
 * @note 专用于处理陀螺仪偏航角在±180°处的跳变补偿
 */
float pid_yaw_calc(pid_t *pid, float get, float set ,uint8_t smoth);

/**
 * @brief 积分分离PID计算函数(防积分饱和)
 * @param pid PID控制器实例指针
 * @param get 当前反馈值
 * @param set 目标设定值
 * @param smoth 平滑滤波标志
 * @param i_separation_value 积分分离阈值
 * @return PID控制输出值
 * @note 当误差大于阈值时停止积分，防止积分饱和和超调
 */
float pid_calc_i_separation(pid_t *pid, float get, float set ,uint8_t smoth,float i_separation_value);

/**
 * @brief 微分先行PID计算函数(减少微分冲击)
 * @param pid PID控制器实例指针
 * @param get 当前反馈值
 * @param set 目标设定值
 * @param actual 当前实际值
 * @param last_actual 上次实际值
 * @param smoth 平滑滤波标志
 * @return PID控制输出值
 * @note 微分项基于实际值变化而非误差变化，减少设定值突变的冲击
 */
float pid_calc_d(pid_t *pid, float get, float set ,float actual,float last_actual,uint8_t smoth);

/**
 * @brief PID状态清零函数
 * @param pid PID控制器实例指针
 * @note 清零所有输出分量，用于PID复位或模式切换
 */
void pid_clear(pid_t *pid);

/* ============================================================================
 *                           当前系统PID实例
 * ============================================================================ */

/**
 * @brief 步进电机位置控制PID实例
 *
 * pid_x: X轴步进电机位置控制
 * pid_y: Y轴步进电机位置控制
 *
 * 默认参数: Kp=0.02, Ki=0, Kd=0, 输出限幅=3, 积分限幅=1
 * 适用于精确位置控制，响应平稳
 */
extern pid_t pid_x;
extern pid_t pid_y;

/**
 * @brief 直流电机速度控制PID实例
 *
 * pid_speed_left:  左电机速度环PID
 * pid_speed_right: 右电机速度环PID
 *
 * 默认参数: Kp=0.5, Ki=0.2, Kd=0, 输出限幅=200, 积分限幅=50
 * 使用增量式PID，适用于速度闭环控制
 */
extern pid_t pid_speed_left,pid_speed_right;

/**
 * @brief 位置外环控制PID实例
 *
 * pid_location_left:  左侧位置外环PID
 * pid_location_right: 右侧位置外环PID
 *
 * 默认参数: Kp=0.1, Ki=0, Kd=0, 输出限幅=40, 积分限幅=0
 * 用于双环控制系统的外环位置控制
 */
extern pid_t pid_location_left,pid_location_right;

/* ============================================================================
 *                           系统配置常量
 * ============================================================================ */

/**
 * @brief 位置控制容差配置
 *
 * 这些参数定义了位置控制的精度要求，当位置误差小于容差时
 * 可以认为已到达目标位置，避免不必要的微调振荡
 */
#define TOLERANCE_CM_X 1.0f     // X轴位置容差范围(cm)
#define TOLERANCE_CM_Y 1.0f     // Y轴位置容差范围(cm)

/**
 * @brief 像素-物理距离转换系数
 *
 * 用于图像处理中像素坐标与实际物理距离的转换
 * 需要根据摄像头参数和机械结构进行实际标定
 *
 * 标定方法:
 * 1. 在图像中测量已知长度物体的像素数
 * 2. 计算: PIXELS_PER_CM = 像素数 / 实际长度(cm)
 * 3. 多次测量取平均值提高精度
 */
#define PIXELS_PER_CM_X 13.33f  // X轴像素/厘米转换系数
#define PIXELS_PER_CM_Y 13.33f  // Y轴像素/厘米转换系数

/* ============================================================================
 *                           代码解释总结
 * ============================================================================
 *
 * 【已实现的PID算法特点】
 *
 * 1. 标准PID (pid_calc):
 *    - 支持位置式和增量式两种模式
 *    - 包含输入/输出死区处理
 *    - 支持误差平滑滤波
 *    - 完整的限幅保护
 *
 * 2. 积分分离PID (pid_calc_i_separation):
 *    - 大误差时停止积分，防止积分饱和
 *    - 支持积分衰减和过零检测
 *    - 渐变启用积分项，减少抖动
 *
 * 3. 微分先行PID (pid_calc_d):
 *    - 微分项基于实际值变化，减少设定值冲击
 *    - 适用于设定值频繁变化的场合
 *
 * 4. 角度专用PID:
 *    - pid_angle_calc: 处理0-360°循环
 *    - pid_yaw_calc: 处理±180°跳变补偿
 *
 * 【系统集成说明】
 * 本模块已完全集成到运动控制系统中，通过PID_INIT()统一初始化
 * 各PID实例，支持步进电机、直流电机、位置控制等多种应用场景。
 *
 * 【调试建议】
 * 1. 先调Kp获得基本响应
 * 2. 加入Kd减少超调
 * 3. 最后调Ki消除静差
 * 4. 根据需要启用死区和限幅保护
 *
 * ============================================================================
 */

#endif

