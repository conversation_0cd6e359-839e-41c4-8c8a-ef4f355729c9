# 步进电机激光追踪系统移植指南

## 📋 项目概述
本指南将帮助您移植基于Emm_V5驱动的步进电机激光追踪系统，实现通过UART1发送坐标控制步进电机追踪目标。

## 🎯 实现目标
- 移植Emm_V5步进电机驱动库
- 移植PID控制算法
- 实现UART1坐标输入替代摄像头反馈
- 建立完整的激光追踪控制系统

## 📡 通信协议说明

### UART1坐标发送格式
```
红色激光点（目标位置）: red:(x,y)\n
绿色激光点（当前位置）: gre:(x,y)\n
```

**示例：**
```
red:(320,240)\n     // 设置目标位置为(320,240)
gre:(300,220)\n     // 设置当前位置为(300,220)
```

**说明：**
- 坐标范围：通常为0-640(X轴) × 0-480(Y轴)，根据实际图像分辨率调整
- 格式严格：必须包含冒号、括号、逗号，以换行符结尾
- 红色点为目标，绿色点为当前位置，PID控制器会让绿色点追踪红色点

## 📁 需要移植的文件

### 🔄 直接复制的文件（无需修改）
```
app/Emm_V5.h          -> 直接复制到新项目app目录
app/Emm_V5.c          -> 直接复制到新项目app目录
app/mypid.h           -> 直接复制到新项目app目录
app/mypid.c           -> 直接复制到新项目app目录
ringbuffer/ringbuffer.h -> 直接复制到新项目ringbuffer目录
ringbuffer/ringbuffer.c -> 直接复制到新项目ringbuffer目录
```

### 📝 需要修改的文件
```
bsp/step_motor_bsp.h  -> 需要根据硬件配置修改
bsp/step_motor_bsp.c  -> 需要根据硬件配置修改
bsp/pi_bsp.h          -> 激光坐标处理模块
bsp/pi_bsp.c          -> 激光坐标处理模块
bsp/bsp_system.h      -> 系统头文件，添加相关声明
```

## 🚀 详细移植步骤

### 步骤1：创建项目目录结构

在新项目中创建以下目录：
```
新项目/
├── app/              # 应用层库文件
├── bsp/              # 板级支持包
├── ringbuffer/       # 环形缓冲区库
└── Core/             # STM32 HAL核心文件
```

### 步骤2：复制核心库文件

**直接复制以下文件（无需修改）：**

1. **Emm_V5步进电机驱动库**
   ```
   源文件：app/Emm_V5.h
   目标：新项目/app/Emm_V5.h
   
   源文件：app/Emm_V5.c  
   目标：新项目/app/Emm_V5.c
   ```

2. **PID控制库**
   ```
   源文件：app/mypid.h
   目标：新项目/app/mypid.h
   
   源文件：app/mypid.c
   目标：新项目/app/mypid.c
   ```

3. **环形缓冲区库**
   ```
   源文件：ringbuffer/ringbuffer.h
   目标：新项目/ringbuffer/ringbuffer.h
   
   源文件：ringbuffer/ringbuffer.c
   目标：新项目/ringbuffer/ringbuffer.c
   ```

### 步骤3：创建BSP系统头文件

**创建文件：`bsp/bsp_system.h`**

```c
#ifndef __BSP_SYSTEM_H__
#define __BSP_SYSTEM_H__

#include <stdarg.h>
#include <string.h>
#include <stdbool.h>
#include <stdlib.h>
#include <stdint.h>
#include <stdio.h>
#include <math.h>

#include "main.h"
#include "usart.h"
#include "ringbuffer.h"

// 包含移植的库
#include "Emm_V5.h"
#include "mypid.h"

// 环形缓冲区相关
extern struct rt_ringbuffer ringbuffer_uart1;
extern uint8_t uart1_rx_buf[64];
extern uint8_t ringbuffer_pool_uart1[64];
extern uint8_t output_buffer_uart1[64];

// 步进电机相关
extern struct rt_ringbuffer ringbuffer_x;
extern struct rt_ringbuffer ringbuffer_y;
extern uint8_t motor_x_buf[64];
extern uint8_t motor_y_buf[64];
extern uint8_t ringbuffer_pool_x[64];
extern uint8_t ringbuffer_pool_y[64];
extern uint8_t output_buffer_x[64];
extern uint8_t output_buffer_y[64];

// 串口打印函数声明
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);

#endif
```

### 步骤4：创建激光坐标处理模块

**创建文件：`bsp/pi_bsp.h`**

```c
#ifndef __PI_BSP_H__
#define __PI_BSP_H__

#include "bsp_system.h"

// 激光类型标识符
#define RED_LASER_ID 'R'
#define GREEN_LASER_ID 'G'

// 激光坐标数据结构
typedef struct {
    char type;       // 激光类型: 'R'表示红色激光，'G'表示绿色激光
    int x;           // X坐标
    int y;           // Y坐标
    uint8_t isValid; // 数据有效标志
} LaserCoord_t;

// 函数声明
int pi_parse_data(char *buffer);
void pi_proc(void);

// 全局变量声明
extern LaserCoord_t latest_red_laser_coord;
extern LaserCoord_t latest_green_laser_coord;

#endif
```

**创建文件：`bsp/pi_bsp.c`**

```c
#include "pi_bsp.h"

// 全局激光坐标变量
LaserCoord_t latest_red_laser_coord = {RED_LASER_ID, 0, 0, 0};
LaserCoord_t latest_green_laser_coord = {GREEN_LASER_ID, 0, 0, 0};

/**
 * @brief 解析激光坐标数据
 * @param buffer 接收到的数据缓冲区
 * @return 0-成功，负数-失败
 */
int pi_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // 空指针检查

    int parsed_x, parsed_y;
    int parsed_count;

    // 解析红色激光坐标: red:(x,y)
    if (strncmp(buffer, "red:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
            return -2; // 解析失败

        latest_red_laser_coord.x = parsed_x;
        latest_red_laser_coord.y = parsed_y;
        latest_red_laser_coord.isValid = 1;

        my_printf(&huart1, "Parsed RED: X=%d, Y=%d\r\n", 
                  latest_red_laser_coord.x, latest_red_laser_coord.y);
    }
    // 解析绿色激光坐标: gre:(x,y)
    else if (strncmp(buffer, "gre:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
            return -2; // 解析失败

        latest_green_laser_coord.x = parsed_x;
        latest_green_laser_coord.y = parsed_y;
        latest_green_laser_coord.isValid = 1;

        my_printf(&huart1, "Parsed GRE: X=%d, Y=%d\r\n", 
                  latest_green_laser_coord.x, latest_green_laser_coord.y);
    }
    else
    {
        return -3; // 未知格式
    }

    return 0; // 成功
}

/**
 * @brief PID控制处理函数
 */
void pi_proc(void)
{
    float pos_out_x, pos_out_y;

    // PID计算：让绿色激光点追踪红色激光点
    pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, 
                         latest_red_laser_coord.x, 0);
    pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, 
                         latest_red_laser_coord.y, 0);
    
    // 控制步进电机（注意X轴取负值）
    Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
}
```

### 步骤5：创建步进电机BSP模块

**创建文件：`bsp/step_motor_bsp.h`**

```c
#ifndef __STEP_MOTOR_BSP_H__
#define __STEP_MOTOR_BSP_H__

#include "bsp_system.h"

/* 电机控制宏定义 - 根据实际硬件修改 */
#define MOTOR_X_ADDR        0x01          // X轴电机地址
#define MOTOR_Y_ADDR        0x01          // Y轴电机地址
#define MOTOR_X_UART        huart2        // X轴电机串口
#define MOTOR_Y_UART        huart4        // Y轴电机串口
#define MOTOR_MAX_SPEED     3             // 电机最大转速(RPM)
#define MOTOR_ACCEL         0             // 电机加速度
#define MOTOR_SYNC_FLAG     false         // 电机同步标志

/* 函数声明 */
void Step_Motor_Init(void);
void Step_Motor_Set_Speed_my(float x_rpm, float y_rpm);
void Step_Motor_Stop(void);

#endif
```

**创建文件：`bsp/step_motor_bsp.c`**

```c
#include "step_motor_bsp.h"

/**
 * @brief 步进电机初始化
 */
void Step_Motor_Init(void)
{
    // 使能X轴电机
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);
    
    // 使能Y轴电机
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);
    
    // 初始停止
    Step_Motor_Stop();
}

/**
 * @brief 精确RPM速度控制
 */
void Step_Motor_Set_Speed_my(float x_rpm, float y_rpm)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed_scaled, y_speed_scaled;
    float abs_x_rpm, abs_y_rpm;

    // 限制RPM范围
    if (x_rpm > MOTOR_MAX_SPEED) x_rpm = MOTOR_MAX_SPEED;
    if (x_rpm < -MOTOR_MAX_SPEED) x_rpm = -MOTOR_MAX_SPEED;
    if (y_rpm > MOTOR_MAX_SPEED) y_rpm = MOTOR_MAX_SPEED;
    if (y_rpm < -MOTOR_MAX_SPEED) y_rpm = -MOTOR_MAX_SPEED;

    // 处理X轴方向
    if (x_rpm >= 0.0f) {
        x_dir = 0;
        abs_x_rpm = x_rpm;
    } else {
        x_dir = 1;
        abs_x_rpm = -x_rpm;
    }

    // 处理Y轴方向
    if (y_rpm >= 0.0f) {
        y_dir = 0;
        abs_y_rpm = y_rpm;
    } else {
        y_dir = 1;
        abs_y_rpm = -y_rpm;
    }

    // 计算速度值(单位为0.1RPM)
    x_speed_scaled = (uint16_t)(abs_x_rpm * 10 + 0.5f);
    y_speed_scaled = (uint16_t)(abs_y_rpm * 10 + 0.5f);
    
    // 控制电机
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, 
                       x_speed_scaled, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, 
                       y_speed_scaled, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief 停止所有电机
 */
void Step_Motor_Stop(void)
{
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}
```

## ⚠️ 重要配置说明

### 硬件配置要求
1. **串口配置**：确保UART2、UART4已配置为115200波特率
2. **DMA配置**：为串口接收配置DMA通道
3. **电机地址**：根据实际Emm_V5驱动器地址修改宏定义

### PID参数调整
在新项目的PID_INIT()函数中：
```c
void PID_INIT(void)
{
    // 步进电机位置PID - 根据实际响应调整参数
    PID_struct_init(&pid_x, POSITION_PID, 3, 1, 0.02, 0, 0);
    PID_struct_init(&pid_y, POSITION_PID, 3, 1, 0.02, 0, 0);
}
```

## 🧪 测试方法

1. **编译下载程序**
2. **连接串口调试助手到UART1**
3. **发送测试命令**：
   ```
   red:(320,240)
   gre:(300,220)
   ```
4. **观察电机响应**：步进电机应该开始运动，让绿色点追踪红色点

## 🔧 故障排除

- **电机不动**：检查串口配置和电机使能
- **追踪不准确**：调整PID参数
- **坐标解析失败**：检查发送格式是否正确
- **编译错误**：检查头文件包含路径

完成以上步骤后，您将拥有一个完整的步进电机激光追踪系统！
