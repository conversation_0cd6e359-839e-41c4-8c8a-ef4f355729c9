{"name": "2025template", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32f407xx.s"}], "folders": []}, {"name": "User", "files": [], "folders": [{"name": "Core", "files": [{"path": "../Core/Src/main.c"}, {"path": "../Core/Src/gpio.c"}, {"path": "../Core/Src/dma.c"}, {"path": "../Core/Src/i2c.c"}, {"path": "../Core/Src/tim.c"}, {"path": "../Core/Src/usart.c"}, {"path": "../Core/Src/stm32f4xx_it.c"}, {"path": "../Core/Src/stm32f4xx_hal_msp.c"}], "folders": []}]}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32F4xx_HAL_Driver", "files": [{"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Core/Src/system_stm32f4xx.c"}], "folders": []}]}, {"name": "OLED", "files": [{"path": "../OLED/oled.c"}, {"path": "../OLED/oled.h"}, {"path": "../OLED/oledfont.h"}, {"path": "../OLED/oledpic.h"}], "folders": []}, {"name": "ringbuffer", "files": [{"path": "../ringbuffer/ringbuffer.c"}, {"path": "../ringbuffer/ringbuffer.h"}], "folders": []}, {"name": "bsp", "files": [{"path": "../bsp/bsp_system.h"}, {"path": "../bsp/schedule.c"}, {"path": "../bsp/oled_bsp.c"}, {"path": "../bsp/key_bsp.c"}, {"path": "../bsp/motor_bsp.c"}, {"path": "../bsp/encoder_bsp.c"}, {"path": "../bsp/hwt101_bsp.c"}, {"path": "../bsp/uart_bsp.c"}, {"path": "../bsp/step_motor_bsp.c"}, {"path": "../bsp/pi_bsp.c"}, {"path": "../bsp/gray_bsp.c"}], "folders": []}, {"name": "app", "files": [{"path": "../app/motor_driver.c"}, {"path": "../app/encoder_drv.c"}, {"path": "../app/hwt101_driver.c"}, {"path": "../app/gw_grayscale_sensor.h"}, {"path": "../app/hardware_iic.c"}, {"path": "../app/Emm_V5.c"}, {"path": "../app/mypid.c"}], "folders": []}, {"name": "tb6612", "files": [{"path": "../TB6612/motor_driver_tb6612.c"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "0f0ff25eed1a5fe33d1633727f1c3a68"}, "targets": {"2025template": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "archExtensions": "", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x1c000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x2001c000", "size": "0x4000"}, "isChecked": true, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x80000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["../Core/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F4xx/Include", "../Drivers/CMSIS/Include", "../bsp", "../OLED", "../app", "../ringbuffer", "../TB6612", ".cmsis/include", "RTE/_2025template"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32F407xx"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": false, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "", "rw-base": ""}}}}}, "version": "3.6"}