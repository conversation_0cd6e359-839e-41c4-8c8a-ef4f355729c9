import sensor, image, time
import math

# 矩形识别阶段摄像头参数设置
def setup_rectangle_detection():
    sensor.set_pixformat(sensor.RGB565)
    sensor.set_framesize(sensor.QQVGA)  # 使用QQVGA分辨率进行矩形识别
    sensor.skip_frames(time=2000)

# 激光识别阶段摄像头参数设置
def setup_laser_detection():
    sensor.set_brightness(-3)  # 设置亮度
    sensor.set_contrast(3)  # 对比度
    sensor.set_auto_gain(False)
    sensor.set_auto_whitebal(False)
    sensor.set_auto_exposure(False, exposure_us=int(800))  # 示例：调整曝光时间
    sensor.set_framesize(sensor.QVGA)  # 使用QVGA分辨率进行激光识别

# 向内收缩的值，可根据实际情况调整
shrink_value = 6

# 定义红色的颜色阈值
# 这里的阈值可能需要根据实际情况进行调整
red_threshold =(11, 70, 6, 60, -16, 41)

# 定义绿色的颜色阈值
# 这里的阈值可能需要根据实际情况进行调整
green_threshold = (3, 17, 16, 54, -75, -20)

# 合并红色和绿色的阈值
thresholds = [red_threshold, green_threshold]

# 最大循环次数
MAX_LOOPS = 10
# 角点变化的最大允许差值
MAX_DIFFERENCE = 5
# 记录稳定的原始角点
stable_original_corners = None
# 记录稳定的缩小后角点
stable_shrunk_corners = None
# 连续稳定的次数
stable_count = 0
# 标记是否开始激光识别
start_laser_detection = False
# 标记是否已找到矩形
rectangle_found = False

# 初始化摄像头为矩形识别参数
sensor.reset()
setup_rectangle_detection()

clock = time.clock()

while True:
    clock.tick()
    img = sensor.snapshot()

    if not rectangle_found:
        # 矩形识别阶段
        # 寻找矩形
        rects = img.find_rects(threshold=35000)
        for r in rects:
            original_corners = r.corners()

            # 选择第一个角点作为旋转参考点
            rotate_point = original_corners[0]

            # 计算旋转角度
            theta = -math.atan2(original_corners[1][1] - original_corners[0][1], original_corners[1][0] - original_corners[0][0])

            # 旋转角点
            rotated_corners = []
            for corner in original_corners:
                x, y = corner
                rotated_x = (x - rotate_point[0]) * math.cos(theta) - (y - rotate_point[1]) * math.sin(theta) + rotate_point[0]
                rotated_y = (x - rotate_point[0]) * math.sin(theta) + (y - rotate_point[1]) * math.cos(theta) + rotate_point[1]
                rotated_corners.append((rotated_x, rotated_y))

            # 计算旋转后第一个角点和第三个角点的角度
            theta_p02 = math.atan2(rotated_corners[2][1] - rotated_corners[0][1], rotated_corners[2][0] - rotated_corners[0][0])

            # 收缩旋转后的角点
            shrunk_rotated_corners = []
            if theta_p02 > 0:
                shrunk_rotated_corners.append((rotated_corners[0][0] + shrink_value, rotated_corners[0][1] + shrink_value))
                shrunk_rotated_corners.append((rotated_corners[1][0] - shrink_value, rotated_corners[1][1] + shrink_value))
                shrunk_rotated_corners.append((rotated_corners[2][0] - shrink_value, rotated_corners[2][1] - shrink_value))
                shrunk_rotated_corners.append((rotated_corners[3][0] + shrink_value, rotated_corners[3][1] - shrink_value))
            else:
                shrunk_rotated_corners.append((rotated_corners[0][0] + shrink_value, rotated_corners[0][1] - shrink_value))
                shrunk_rotated_corners.append((rotated_corners[1][0] - shrink_value, rotated_corners[1][1] - shrink_value))
                shrunk_rotated_corners.append((rotated_corners[2][0] - shrink_value, rotated_corners[2][1] + shrink_value))
                shrunk_rotated_corners.append((rotated_corners[3][0] + shrink_value, rotated_corners[3][1] + shrink_value))

            # 反向旋转收缩后的角点
            shrunk_corners = []
            for corner in shrunk_rotated_corners:
                x, y = corner
                new_x = (x - rotate_point[0]) * math.cos(-theta) - (y - rotate_point[1]) * math.sin(-theta) + rotate_point[0]
                new_y = (x - rotate_point[0]) * math.sin(-theta) + (y - rotate_point[1]) * math.cos(-theta) + rotate_point[1]
                shrunk_corners.append((int(new_x), int(new_y)))

            if stable_original_corners is None:
                stable_original_corners = original_corners
                stable_shrunk_corners = shrunk_corners
                stable_count = 1
            else:
                all_stable_original = True
                all_stable_shrunk = True
                for i in range(4):
                    for j in range(2):
                        if abs(original_corners[i][j] - stable_original_corners[i][j]) > MAX_DIFFERENCE:
                            all_stable_original = False
                        if abs(shrunk_corners[i][j] - stable_shrunk_corners[i][j]) > MAX_DIFFERENCE:
                            all_stable_shrunk = False
                    if not all_stable_original or not all_stable_shrunk:
                        break
                if all_stable_original and all_stable_shrunk:
                    stable_count += 1
                else:
                    stable_original_corners = original_corners
                    stable_shrunk_corners = shrunk_corners
                    stable_count = 1

            if stable_count == MAX_LOOPS:
                # 角点稳定，开始激光识别
                rectangle_found = True
                start_laser_detection = True
                setup_laser_detection()

                # 将QQVGA下的坐标转换为QVGA下的坐标
                stable_original_corners = [(x * 2, y * 2) for x, y in stable_original_corners]
                stable_shrunk_corners = [(x * 2, y * 2) for x, y in stable_shrunk_corners]
                break

    if start_laser_detection:
        # 激光识别阶段
        # 激光识别部分
        red_blobs = img.find_blobs([red_threshold], x_stride=1, pixels_threshold=6, area_threshold=2, merge=1)
        green_blobs = img.find_blobs([green_threshold], x_stride=1, pixels_threshold=6, area_threshold=2, merge=1)

        # 绘制激光识别结果
        for blob in red_blobs:
            if blob:
                print("red=:" + str(blob.cx()) + "y=" + str(blob.cy()))
                # 在图像上绘制矩形框标记红色色块
                img.draw_rectangle(blob.rect(), color=(255, 0, 0))
                # 在图像上绘制十字标记红色色块的中心
                img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 0))

        for blob in green_blobs:
            if blob:
                print("green=:" + str(blob.cx()) + "y=" + str(blob.cy()))
                # 在图像上绘制矩形框标记绿色色块
                img.draw_rectangle(blob.rect(), color=(0, 255, 0))
                # 在图像上绘制十字标记绿色色块的中心
                img.draw_cross(blob.cx(), blob.cy(), color=(0, 255, 0))

        # 绘制原始矩形
        for i in range(4):
            p1 = stable_original_corners[i]
            p2 = stable_original_corners[(i + 1) % 4]
            img.draw_line(p1[0], p1[1], p2[0], p2[1], color=(0, 255, 0))

        # 绘制缩小后的矩形
        for i in range(4):
            p1 = stable_shrunk_corners[i]
            p2 = stable_shrunk_corners[(i + 1) % 4]
            img.draw_line(p1[0], p1[1], p2[0], p2[1], color=(255, 0, 0))

    # 计算帧率
    fps = clock.fps()
    # 在 RGB 图像上显示帧率
    img.draw_string(10, 10, f"FPS: {fps:.2f}", color=(255, 255, 255), scale=1)
